/**
 * IndexedDB 数据库操作工具
 * 用于在Web环境下存储聊天消息和相关数据
 * 参考 chat-app/utils/db.js 的逻辑和字段结构
 */

// 数据库配置
export const DB_NAME = 'chatDb'
export const DB_VERSION = 1

// 获取当前用户ID
const getCurrentUserId = () => {
  return localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'
}

// 获取表名
export const getTableName = () => `chatData_${getCurrentUserId()}`

// 数据库实例
let db = null

/**
 * 打开数据库
 * @returns {Promise<IDBDatabase>}
 */
export const openDb = () => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = () => {
      console.error('数据库打开失败:', request.error)
      reject(request.error)
    }

    request.onsuccess = () => {
      db = request.result
      console.log('数据库打开成功')
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      db = event.target.result
      console.log('数据库升级中...')
      
      const tableName = getTableName()
      if (!db.objectStoreNames.contains(tableName)) {
        const store = db.createObjectStore(tableName, { keyPath: 'id' })
        
        // 创建索引
        store.createIndex('chatid', 'chatid', { unique: false })
        store.createIndex('fromid', 'fromid', { unique: false })
        store.createIndex('toid', 'toid', { unique: false })
        store.createIndex('t', 't', { unique: false })
        store.createIndex('typecode', 'typecode', { unique: false })
        store.createIndex('typecode2', 'typecode2', { unique: false })
        
        console.log('消息表创建成功:', tableName)
      }
    }
  })
}

/**
 * 关闭数据库
 */
export const closeDb = () => {
  if (db) {
    db.close()
    db = null
    console.log('数据库已关闭')
  }
}

/**
 * 创建表
 * @returns {Promise<boolean>}
 */
export const addTab = async () => {
  try {
    await openDb()
    return true
  } catch (error) {
    console.error('创建表失败:', error)
    return false
  }
}

/**
 * 添加消息到数据库
 * @param {Object} item - 消息对象
 * @returns {Promise<Object>}
 */
export const addTabItem = (item) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!item) {
        reject(new Error('参数不能为空'))
        return
      }

      if (!db) {
        await openDb()
      }

      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())

      // 处理数据字段
      const processedItem = {
        id: Number(item.id) || Date.now(),
        typecode: Number(item.typecode) || 0,
        typecode2: Number(item.typecode2) || 0,
        toid: String(item.toid || ''),
        fromid: String(item.fromid || ''),
        chatid: String(item.chatid || ''),
        t: String(item.t || new Date().toISOString()),
        isRedRead: Number(item.isRedRead) || 0,
        idDel: Number(item.idDel) || 0,
        msg: String(item.msg || ''),
        senderAvatar: String(item.senderAvatar || ''),
        senderNickname: String(item.senderNickname || ''),
        avatar: String(item.avatar || ''),
        nickname: String(item.nickname || ''),
        lastMessage: String(item.lastMessage || ''),
        timestamp: Number(item.timestamp) || new Date().getTime(),
        unreadCount: Number(item.unreadCount) || 0
      }

      // 验证必要字段
      if (!processedItem.toid || !processedItem.fromid || !processedItem.chatid) {
        reject(new Error('必要字段缺失：toid, fromid, chatid'))
        return
      }

      const request = store.put(processedItem)

      request.onsuccess = () => {
        console.log('消息添加成功:', processedItem.id)
        resolve(processedItem)
      }

      request.onerror = () => {
        console.error('消息添加失败:', request.error)
        reject(request.error)
      }

    } catch (error) {
      console.error('添加消息处理错误:', error)
      reject(error)
    }
  })
}

/**
 * 根据chatId获取聊天消息
 * @param {string} chatId - 聊天对象ID
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 * @returns {Promise<Array>}
 */
export const getChatMessages = (chatId, page = 1, size = 20) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!chatId) {
        reject(new Error('chatId is required'))
        return
      }

      if (!db) {
        await openDb()
      }

      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')
      
      const request = index.getAll(chatId.toString())

      request.onsuccess = () => {
        let messages = request.result || []
        
        // 按时间倒序排序
        messages.sort((a, b) => new Date(b.t).getTime() - new Date(a.t).getTime())
        
        // 分页处理
        const startIndex = (page - 1) * size
        const endIndex = startIndex + size
        const paginatedMessages = messages.slice(startIndex, endIndex)
        
        console.log(`获取聊天消息: chatId=${chatId}, 总数=${messages.length}, 返回=${paginatedMessages.length}`)
        resolve(paginatedMessages)
      }

      request.onerror = () => {
        console.error('获取聊天消息失败:', request.error)
        reject(request.error)
      }

    } catch (error) {
      console.error('获取聊天消息处理错误:', error)
      reject(error)
    }
  })
}

/**
 * 获取聊天对象列表
 * @returns {Promise<Array>}
 */
export const getChatList = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!db) {
        await openDb()
      }

      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const messages = request.result || []
        
        // 按chatid分组，获取每个聊天对象的最新消息
        const chatMap = new Map()
        
        messages.forEach(message => {
          const chatId = message.chatid
          if (!chatMap.has(chatId) || new Date(message.t) > new Date(chatMap.get(chatId).t)) {
            chatMap.set(chatId, message)
          }
        })
        
        // 转换为数组并按时间排序
        const chatList = Array.from(chatMap.values())
          .sort((a, b) => new Date(b.t).getTime() - new Date(a.t).getTime())
          .map(message => ({
            chatId: message.chatid,
            name: message.nickname || message.senderNickname || `用户${message.chatid}`,
            avatar: message.avatar || message.senderAvatar || '/static/My/avatar.jpg',
            lastMessage: message.lastMessage || message.msg || '',
            lastTime: message.t,
            timestamp: message.timestamp,
            unreadCount: message.unreadCount || 0,
            type: message.typecode === 2 ? 'group' : 'contact'
          }))
        
        console.log('获取聊天列表成功:', chatList.length, '个')
        resolve(chatList)
      }

      request.onerror = () => {
        console.error('获取聊天列表失败:', request.error)
        reject(request.error)
      }

    } catch (error) {
      console.error('获取聊天列表处理错误:', error)
      reject(error)
    }
  })
}

/**
 * 标记消息为已读
 * @param {string} chatId - 聊天对象ID
 * @returns {Promise<boolean>}
 */
export const markMessagesAsRead = (chatId) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!chatId) {
        reject(new Error('chatId is required'))
        return
      }

      if (!db) {
        await openDb()
      }

      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')
      
      const request = index.getAll(chatId.toString())

      request.onsuccess = () => {
        const messages = request.result || []
        const unreadMessages = messages.filter(m => m.isRedRead === 0)
        
        if (unreadMessages.length === 0) {
          resolve(true)
          return
        }
        
        let updateCount = 0
        
        unreadMessages.forEach(message => {
          message.isRedRead = 1
          
          const updateRequest = store.put(message)
          
          updateRequest.onsuccess = () => {
            updateCount++
            if (updateCount === unreadMessages.length) {
              console.log(`标记消息为已读: chatId=${chatId}, 更新数量=${updateCount}`)
              resolve(true)
            }
          }
          
          updateRequest.onerror = () => {
            console.error('更新消息已读状态失败:', updateRequest.error)
            reject(updateRequest.error)
          }
        })
      }

      request.onerror = () => {
        console.error('获取消息失败:', request.error)
        reject(request.error)
      }

    } catch (error) {
      console.error('标记消息为已读处理错误:', error)
      reject(error)
    }
  })
}
